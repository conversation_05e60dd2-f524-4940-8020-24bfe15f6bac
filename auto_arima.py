import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pmdarima.arima import auto_arima

# Bước 1: Đ<PERSON><PERSON> dữ liệu
df = pd.read_csv('history.csv')
df['time'] = pd.to_datetime(df['time'])
df.set_index('time', inplace=True)

# Bước 2: Log-transform để ổn định phương sai (nếu cần)
log_close = np.log(df['close'])

# Bước 3: Tì<PERSON> mô hình ARIMA tốt nhất
model_auto = auto_arima(
    log_close,
    start_p=0, start_q=0,
    max_p=5, max_q=5,
    seasonal=False,
    d=None,            # Tự động xác định sai phân
    trace=True,        # Hiển thị quá trình dò tìm
    error_action='ignore',
    suppress_warnings=True,
    stepwise=True
)

print("<PERSON><PERSON> hình ARIMA tốt nhất:", model_auto.summary())

# Bước 4: <PERSON><PERSON> đoán 48 bước tiếp theo
forecast_log = model_auto.predict(n_periods=48)
forecast = np.exp(forecast_log)

# Bước 5: Vẽ kết quả
plt.figure(figsize=(14, 6))
plt.plot(df['close'][-200:], label='Giá thực')
plt.plot(pd.date_range(df.index[-1], periods=48, freq='15min'), forecast, label='Dự đoán ARIMA', color='red')
plt.title('Dự đoán tự động bằng ARIMA')
plt.xlabel('Thời gian')
plt.ylabel('Giá')
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.show()
