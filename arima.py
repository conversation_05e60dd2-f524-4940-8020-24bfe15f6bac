import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf

# Bước 1: Đọ<PERSON> dữ liệu
df = pd.read_csv('history.csv')
df['time'] = pd.to_datetime(df['time'])
df.set_index('time', inplace=True)

# Bước 2: <PERSON><PERSON><PERSON><PERSON> gi<PERSON> sang log để ổn định phương sai (tùy chọn)
log_close = np.log(df['close'])

# Bước 3: Vẽ ACF/PACF để ước lượng p, d, q
plot_acf(log_close.diff().dropna(), lags=50)
plt.title('ACF')
plt.show()

plot_pacf(log_close.diff().dropna(), lags=50)
plt.title('PACF')
plt.show()

# Bước 4: <PERSON><PERSON><PERSON> luy<PERSON><PERSON> mô hình ARIMA (ví dụ: (3,1,2))
model = ARIMA(log_close, order=(3, 1, 2))
model_fit = model.fit()

# Bước 5: Dự đoán 48 bước tiếp theo (12 giờ nếu mỗi bước = 15 phút)
forecast_log = model_fit.forecast(steps=48)
forecast = np.exp(forecast_log)  # Chuyển về giá thật

# Bước 6: Vẽ biểu đồ kết quả
plt.figure(figsize=(14,6))
plt.plot(df['close'][-200:], label='Giá đóng cửa thực')
plt.plot(forecast.index, forecast, label='Dự đoán ARIMA', color='red')
plt.xlabel('Thời gian')
plt.ylabel('Giá')
plt.title('Dự đoán giá với mô hình ARIMA')
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.show()
