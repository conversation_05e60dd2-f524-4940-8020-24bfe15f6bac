import pandas as pd
import matplotlib.pyplot as plt
from prophet import Prophet

# Bước 1: Đ<PERSON><PERSON> dữ liệu
df = pd.read_csv('history.csv')
df['time'] = pd.to_datetime(df['time'])

# Bước 2: <PERSON><PERSON><PERSON> bị dữ liệu cho Prophet
df_prophet = df[['time', 'close']].rename(columns={'time': 'ds', 'close': 'y'})

# Bước 3: Khởi tạo mô hình Prophet
model = Prophet(daily_seasonality=True)  # C<PERSON> thể thêm weekly/yearly nếu cần

# Bước 4: Huấn luyện mô hình
model.fit(df_prophet)

# Bước 5: D<PERSON> đoán 48 bước tiếp theo (12 giờ với dữ liệu 15 phút)
future = model.make_future_dataframe(periods=48, freq='15min')
forecast = model.predict(future)

# Bước 6: Vẽ kết quả
fig1 = model.plot(forecast)
plt.title('Dự đoán giá với Prophet')
plt.xlabel('Thời gian')
plt.ylabel('Gi<PERSON>')
plt.grid(True)
plt.show()

# Bước 7: Vẽ thành phần xu hướng/chu kỳ
fig2 = model.plot_components(forecast)
plt.tight_layout()
plt.show()
